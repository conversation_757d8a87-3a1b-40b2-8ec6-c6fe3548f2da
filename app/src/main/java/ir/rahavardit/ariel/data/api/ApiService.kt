package ir.rahavardit.ariel.data.api

import ir.rahavardit.ariel.data.model.BankResponse
import ir.rahavardit.ariel.data.model.CategoriesResponse
import ir.rahavardit.ariel.data.model.CategoryUpdateRequest
import ir.rahavardit.ariel.data.model.FAQ
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.model.LoginRequest
import ir.rahavardit.ariel.data.model.LoginResponse
import ir.rahavardit.ariel.data.model.NewTicketRequest
import ir.rahavardit.ariel.data.model.NewTransactionRequest
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.data.model.PasswordChangeRequest
import ir.rahavardit.ariel.data.model.Priority
import ir.rahavardit.ariel.data.model.PriorityUpdateRequest
import ir.rahavardit.ariel.data.model.Profile
import ir.rahavardit.ariel.data.model.RatingRequest
import ir.rahavardit.ariel.data.model.SearchResult
import ir.rahavardit.ariel.data.model.Status
import ir.rahavardit.ariel.data.model.StatusUpdateRequest
import ir.rahavardit.ariel.data.model.TagResponse
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.data.model.HomepageDataResponse
import ir.rahavardit.ariel.data.model.TransactionCategoryResponse
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.data.model.UserListItem
import ir.rahavardit.ariel.data.model.UserListResponse
import ir.rahavardit.ariel.data.model.UserSignature
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * Interface defining the API endpoints for the Ariel app.
 */
interface ApiService {

    /**
     * Authenticates a user and retrieves a token.
     *
     * @param loginRequest The login credentials.
     * @return A Response containing the login response with token and user information.
     */
    @POST("/api/token/")
    suspend fun login(@Body loginRequest: LoginRequest): Response<LoginResponse>

    /**
     * Logs out the user and invalidates the authentication token on the backend.
     *
     * @param token The authentication token.
     * @return A Response with void content on success.
     */
    @POST("/api/logout/")
    suspend fun logout(@Header("Authorization") token: String): Response<Void>

    /**
     * Retrieves the list of tickets for the authenticated user.
     *
     * @param token The authentication token.
     * @param pageSize The number of tickets per page.
     * @return A Response containing a paginated list of tickets.
     */
    @GET("/api/tickets/")
    suspend fun getMyTickets(
        @Header("Authorization") token: String,
        @Query("page_size") pageSize: Int = 15
    ): Response<PaginatedResponse<Ticket>>

    /**
     * Retrieves tickets from a specific URL (for pagination).
     *
     * @param token The authentication token.
     * @param url The full URL for the next page.
     * @return A Response containing a paginated list of tickets.
     */
    @GET
    suspend fun getTicketsFromUrl(
        @Header("Authorization") token: String,
        @retrofit2.http.Url url: String
    ): Response<PaginatedResponse<Ticket>>

    /**
     * Retrieves the list of tickets for the authenticated user filtered by status.
     *
     * @param token The authentication token.
     * @param status The status to filter by.
     * @return A Response containing a paginated list of tickets.
     */
    @GET("/api/tickets/")
    suspend fun getMyTicketsByStatus(
        @Header("Authorization") token: String,
        @Query("status") status: String
    ): Response<PaginatedResponse<Ticket>>

    /**
     * Retrieves the list of tickets for the authenticated user filtered by group ID.
     *
     * @param token The authentication token.
     * @param groupId The group ID to filter by.
     * @return A Response containing a paginated list of tickets.
     */
    @GET("/api/tickets/")
    suspend fun getMyTicketsByGroupId(
        @Header("Authorization") token: String,
        @Query("group-id") groupId: Int
    ): Response<PaginatedResponse<Ticket>>

    /**
     * Retrieves the profile information for the authenticated user.
     *
     * @param token The authentication token.
     * @return A Response containing the user's profile information.
     */
    @GET("/api/profile/")
    suspend fun getProfile(@Header("Authorization") token: String): Response<Profile>

    /**
     * Retrieves the details of a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @return A Response containing the ticket details.
     */
    @GET("/api/tickets/{short_uuid}/")
    suspend fun getTicketDetails(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<Ticket>

    /**
     * Retrieves the list of available categories.
     *
     * @param token The authentication token.
     * @return A Response containing the list of categories.
     */
    @GET("/api/categories/")
    suspend fun getCategories(@Header("Authorization") token: String): Response<CategoriesResponse>

    /**
     * Retrieves the list of available priorities.
     *
     * @param token The authentication token.
     * @return A Response containing the list of priorities.
     */
    @GET("/api/priorities/")
    suspend fun getPriorities(@Header("Authorization") token: String): Response<List<Priority>>

    /**
     * Creates a new ticket.
     *
     * @param token The authentication token.
     * @param newTicketRequest The new ticket request.
     * @return A Response containing the created ticket.
     */
    @POST("/api/tickets/")
    suspend fun createTicket(
        @Header("Authorization") token: String,
        @Body newTicketRequest: NewTicketRequest
    ): Response<Ticket>

    /**
     * Creates a new ticket with a file attachment.
     *
     * @param token The authentication token.
     * @param title The title of the ticket.
     * @param message The message content of the ticket.
     * @param category The category of the ticket.
     * @param priority The priority of the ticket.
     * @param file The file to attach to the ticket.
     * @return A Response containing the created ticket.
     */
    @Multipart
    @POST("/api/tickets/")
    suspend fun createTicketWithFile(
        @Header("Authorization") token: String,
        @Part("title") title: RequestBody,
        @Part("message") message: RequestBody,
        @Part("category") category: RequestBody,
        @Part("priority") priority: RequestBody,
        @Part file: MultipartBody.Part
    ): Response<Ticket>

    /**
     * Replies to an existing ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     * @return A Response containing the created reply ticket.
     */
    @POST("/api/reply-ticket/{short_uuid}/")
    suspend fun replyToTicket(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body message: Map<String, String>
    ): Response<Ticket>

    /**
     * Replies to an existing ticket with a file attachment.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     * @param file The file to attach to the reply.
     * @return A Response containing the created reply ticket.
     */
    @Multipart
    @POST("/api/reply-ticket/{short_uuid}/")
    suspend fun replyToTicketWithFile(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Part("message") message: RequestBody,
        @Part file: MultipartBody.Part
    ): Response<Ticket>

    /**
     * Retrieves the signature of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user whose signature to retrieve.
     * @return A Response containing the user's signature.
     */
    @GET("/api/get-user-signature/{short_uuid}/")
    suspend fun getUserSignature(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<UserSignature>

    /**
     * Retrieves the full name of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user whose full name to retrieve.
     * @return A Response containing the user's full name as a string.
     */
    @GET("/api/get-full-name-of-user/{short_uuid}/")
    suspend fun getUserFullName(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<String>











    /**
     * Updates the category of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param categoryRequest The category update request.
     * @return A Response containing the updated ticket.
     */
    @POST("/api/set-ticket-category/{short_uuid}/")
    suspend fun setTicketCategory(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body categoryRequest: CategoryUpdateRequest
    ): Response<Ticket>

    /**
     * Updates the priority of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param priorityRequest The priority update request.
     * @return A Response containing the updated ticket.
     */
    @POST("/api/set-ticket-priority/{short_uuid}/")
    suspend fun setTicketPriority(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body priorityRequest: PriorityUpdateRequest
    ): Response<Ticket>

    /**
     * Retrieves the list of available statuses.
     *
     * @param token The authentication token.
     * @return A Response containing the list of statuses.
     */
    @GET("/api/statuses/")
    suspend fun getStatuses(@Header("Authorization") token: String): Response<List<Status>>

    /**
     * Updates the status of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param statusRequest The status update request.
     * @return A Response containing the updated ticket.
     */
    @POST("/api/set-ticket-status/{short_uuid}/")
    suspend fun setTicketStatus(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body statusRequest: StatusUpdateRequest
    ): Response<Ticket>

    /**
     * Deletes a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket to delete.
     * @return A Response with void content on success.
     */
    @DELETE("/api/tickets/{short_uuid}/")
    suspend fun deleteTicket(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<Void>

    /**
     * Rates a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket to rate.
     * @param ratingRequest The rating request containing the rate value.
     * @return A Response containing the updated ticket.
     */
    @POST("/api/rate-ticket/{short_uuid}/")
    suspend fun rateTicket(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body ratingRequest: RatingRequest
    ): Response<Ticket>

    /**
     * Retrieves the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Response containing the list of groups the user belongs to.
     */
    @GET("/api/get-groups-of-user/{short_uuid}/")
    suspend fun getUserGroups(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<List<String>>

    /**
     * Retrieves the list of all users.
     *
     * @param token The authentication token.
     * @return A Response containing a paginated list of users.
     */
    @GET("/api/users/")
    suspend fun getUsers(@Header("Authorization") token: String): Response<UserListResponse>

    /**
     * Retrieves the categories of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Response containing the list of categories the user belongs to.
     */
    @GET("/api/get-categories-of-user/{short_uuid}/")
    suspend fun getUserCategories(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<List<String>>

    /**
     * Changes the password for the authenticated user.
     *
     * @param token The authentication token.
     * @param passwordChangeRequest The password change request containing old and new passwords.
     * @return A Response with void content on success.
     */
    @POST("/api/change-password/")
    suspend fun changePassword(
        @Header("Authorization") token: String,
        @Body passwordChangeRequest: PasswordChangeRequest
    ): Response<Void>









    /**
     * Searches for content across tickets.
     *
     * @param token The authentication token.
     * @param query The search query string.
     * @return A Response containing the search results.
     */
    @GET("/api/search/")
    suspend fun search(
        @Header("Authorization") token: String,
        @Query("q") query: String
    ): Response<SearchResult>

    /**
     * Retrieves homepage data including statistics for the authenticated user.
     *
     * @param token The authentication token.
     * @param year The chosen year parameter (optional).
     * @param monthStart The chosen month start parameter (optional).
     * @param monthEnd The chosen month end parameter (optional).
     * @return A Response containing homepage data.
     */
    @GET("/api/get-homepage-statistics/")
    suspend fun getHomepageData(
        @Header("Authorization") token: String,
        @Query("year") year: Int? = null,
        @Query("month-start") monthStart: Int? = null,
        @Query("month-end") monthEnd: Int? = null
    ): Response<HomepageDataResponse>

    /**
     * Retrieves the list of transaction modes.
     *
     * @param token The authentication token.
     * @return A Response containing the list of transaction modes.
     */
    @GET("/api/modes/")
    suspend fun getTransactionModes(@Header("Authorization") token: String): Response<List<TransactionMode>>

    /**
     * Retrieves the list of banks.
     *
     * @param token The authentication token.
     * @return A Response containing the paginated list of banks.
     */
    @GET("/api/banks/")
    suspend fun getBanks(@Header("Authorization") token: String): Response<BankResponse>

    /**
     * Retrieves the list of categories for transactions.
     *
     * @param token The authentication token.
     * @return A Response containing the paginated list of categories.
     */
    @GET("/api/categories/")
    suspend fun getTransactionCategories(@Header("Authorization") token: String): Response<TransactionCategoryResponse>

    /**
     * Retrieves the list of tags.
     *
     * @param token The authentication token.
     * @return A Response containing the paginated list of tags.
     */
    @GET("/api/tags/")
    suspend fun getTags(@Header("Authorization") token: String): Response<TagResponse>

    /**
     * Creates a new transaction.
     *
     * @param token The authentication token.
     * @param request The new transaction request data.
     * @return A Response containing the created transaction.
     */
    @POST("/api/transactions/")
    suspend fun createTransaction(
        @Header("Authorization") token: String,
        @Body request: NewTransactionRequest
    ): Response<TransactionResponse>

    /**
     * Updates a specific transaction by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the transaction to update.
     * @param request The updated transaction data.
     * @return A Response containing the updated transaction.
     */
    @retrofit2.http.PUT("/api/transactions/{short_uuid}/")
    suspend fun updateTransaction(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String,
        @Body request: NewTransactionRequest
    ): Response<TransactionResponse>

    /**
     * Deletes a specific transaction by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the transaction to delete.
     * @return A Response with void content on success.
     */
    @DELETE("/api/transactions/{short_uuid}/")
    suspend fun deleteTransaction(
        @Header("Authorization") token: String,
        @Path("short_uuid") shortUuid: String
    ): Response<Void>
}
